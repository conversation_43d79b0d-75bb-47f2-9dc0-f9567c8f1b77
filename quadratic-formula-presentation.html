<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quadratic Formula - Year 10 GCSE Mathematics</title>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
            color: #333;
        }
        
        .presentation-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .slide {
            /* A4 dimensions: 210mm x 297mm, using 96dpi (1mm = 3.78px) */
            width: 794px; /* 210mm */
            height: 1123px; /* 297mm */
            margin: 0 auto;
            padding: 60px;
            box-sizing: border-box;
            position: relative;
            page-break-after: always;
            display: flex;
            flex-direction: column;
            justify-content: center;
            overflow: hidden;
        }
        
        .slide-content {
            width: 100%;
            max-width: 1080px;
            margin: 0 auto;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .slide-footer {
            position: absolute;
            bottom: 20px;
            left: 0;
            right: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #666;
            font-size: 14px;
        }
        
        .slide-footer-text {
            flex: 1;
            text-align: center;
        }
        
        .slide-number {
            position: absolute;
            right: 20px;
            color: #666;
            font-size: 14px;
        }
        
        /* Slide 1 - Title Slide */
        .title-slide {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }
        
        .title-slide h1 {
            font-size: 48px;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .title-slide .subtitle {
            font-size: 24px;
            margin-bottom: 10px;
            opacity: 0.9;
        }
        
        .title-slide .context {
            font-size: 20px;
            opacity: 0.8;
            margin-bottom: 40px;
        }
        
        .parabola-visual {
            margin: 40px auto;
            text-align: center;
        }
        
        .parabola-svg {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 20px;
        }
        
        /* Slide 2 - Learning Objectives */
        .objectives-slide {
            background: #f8f9fa;
        }
        
        .objectives-slide h2 {
            color: #2c5aa0;
            font-size: 36px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .objectives-container {
            display: flex;
            gap: 30px;
            margin-top: 40px;
        }
        
        .objectives-box, .success-box {
            flex: 1;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .objectives-box h3, .success-box h3 {
            color: #2c5aa0;
            margin-bottom: 20px;
            font-size: 24px;
        }
        
        .objective-item, .success-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
            font-size: 18px;
            line-height: 1.5;
        }
        
        .objective-icon {
            font-size: 24px;
            margin-right: 15px;
            flex-shrink: 0;
        }
        
        .grade-badge {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .bronze { background: #cd7f32; color: white; }
        .silver { background: #c0c0c0; color: #333; }
        .gold { background: #ffd700; color: #333; }
        
        .timing-info {
            position: absolute;
            top: 20px;
            right: 20px;
            background: #e3f2fd;
            padding: 10px 20px;
            border-radius: 20px;
            color: #2c5aa0;
            font-weight: bold;
        }
        
        /* Slide 3 - Starter Activities */
        .starter-slide {
            background: #fff3e0;
        }
        
        .starter-slide h2 {
            color: #f57c00;
            font-size: 36px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .activities-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin-top: 40px;
        }
        
        .activity-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .activity-card h3 {
            color: #f57c00;
            margin-bottom: 15px;
            font-size: 20px;
        }
        
        .activity-time {
            background: #ffe0b2;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 14px;
            display: inline-block;
            margin-bottom: 15px;
        }
        
        .drag-drop-demo {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
        }
        
        .equation-box {
            background: #e3f2fd;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            cursor: move;
            transition: transform 0.2s;
        }
        
        .equation-box:hover {
            transform: translateX(5px);
        }
        
        .think-pair-share-question {
            background: #e8f5e9;
            padding: 20px;
            border-radius: 10px;
            margin-top: 10px;
            font-style: italic;
            font-size: 16px;
            text-align: center;
        }
        
        .whiteboard-example {
            background: white;
            border: 3px solid #ddd;
            padding: 20px;
            border-radius: 5px;
            margin-top: 10px;
            text-align: center;
            font-family: 'Comic Sans MS', cursive;
            font-size: 18px;
        }
        
        /* Slide 4 - Main Activities */
        .activities-slide {
            background: #e8f5e9;
        }
        
        .activities-slide h2 {
            color: #388e3c;
            font-size: 36px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .differentiation-banner {
            background: #e0f7fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
            font-size: 16px;
        }
        
        .differentiation-banner strong {
            color: #0097a7;
        }
        
        .activities-flow {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }
        
        .activity-box {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            position: relative;
        }
        
        .activity-box h3 {
            color: #388e3c;
            margin-bottom: 15px;
            font-size: 20px;
        }
        
        .worked-example {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.8;
            overflow-x: auto;
        }
        
        .differentiation-note {
            background: #fff3e0;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-size: 14px;
        }
        
        .exam-question {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #2196f3;
        }
        
        .group-work-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #4caf50;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
        }
        
        .support-note {
            background: #fff9c4;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-size: 14px;
        }
        
        /* Slide 5 - Plenary */
        .plenary-slide {
            background: #f3e5f5;
        }
        
        .plenary-slide h2 {
            color: #7b1fa2;
            font-size: 36px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .plenary-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .plenary-section {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .plenary-section h3 {
            color: #7b1fa2;
            margin-bottom: 15px;
            font-size: 20px;
        }
        
        .exit-ticket {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
        }
        
        .exit-ticket ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .traffic-lights {
            display: flex;
            justify-content: space-around;
            margin-top: 20px;
        }
        
        .traffic-light {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            font-size: 14px;
            text-align: center;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .traffic-light:hover {
            transform: scale(1.1);
        }
        
        .red { background: #f44336; }
        .amber { background: #ff9800; }
        .green { background: #4caf50; }
        
        .homework-box {
            background: #e8eaf6;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
        }
        
        .next-lesson-preview {
            background: #e0f2f1;
            padding: 20px;
            border-radius: 10px;
            margin-top: 10px;
            text-align: center;
            font-size: 16px;
        }
        
        .closing-message {
            text-align: center;
            margin-top: 30px;
            font-size: 20px;
            color: #7b1fa2;
            padding: 20px;
            background: rgba(255,255,255,0.8);
            border-radius: 10px;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 0;
            }
            
            .presentation-container {
                box-shadow: none;
            }
            
            .slide {
                page-break-after: always;
                width: 210mm;
                height: 297mm;
                margin: 0;
                padding: 40px;
            }
        }
        
        @media (max-width: 768px) {
            .slide {
                padding: 30px;
                width: 100vw;
                height: auto;
                min-height: 100vh;
            }
            
            .objectives-container,
            .activities-grid,
            .activities-flow,
            .plenary-grid {
                grid-template-columns: 1fr;
            }
            
            .traffic-lights {
                flex-direction: column;
                align-items: center;
                gap: 10px;
            }
            
            .download-button {
                bottom: 20px;
                right: 20px;
            }
        }
        
        /* Download Button Styles */
        .download-button {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: #2c5aa0;
            color: white;
            border: none;
            border-radius: 50px;
            padding: 15px 30px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        .download-button:hover {
            background: #1e3d6f;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
        }
        
        .download-button:active {
            transform: translateY(0);
        }
        
        .download-icon {
            width: 20px;
            height: 20px;
        }
        
        @media print {
            .download-button {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="presentation-container">
        <div class="slide title-slide">
            <div class="slide-content">
                <h1>The Quadratic Formula</h1>
                <div class="subtitle">A 5-minute teaching demonstration</div>
                <div class="context">Year 10 GCSE Mathematics (Ages 14-15)</div>
                <div class="parabola-visual">
                    <svg class="parabola-svg" width="400" height="250" viewBox="0 0 400 250">
                        <line x1="0" y1="230" x2="400" y2="230" stroke="#fff" stroke-width="3" opacity="0.5"/>
                        
                        <path d="M 20,220 Q 200,50 380,220" fill="none" stroke="#ffeb3b" stroke-width="3" stroke-dasharray="5,5"/>
                        
                        <circle cx="20" cy="220" r="10" fill="#ff6b35"/>
                        <circle cx="100" cy="140" r="10" fill="#ff6b35" opacity="0.7"/>
                        <circle cx="200" cy="60" r="10" fill="#ff6b35" opacity="0.7"/>
                        <circle cx="300" cy="140" r="10" fill="#ff6b35" opacity="0.7"/>
                        <circle cx="380" cy="220" r="10" fill="#ff6b35"/>
                        
                        <rect x="360" y="150" width="5" height="70" fill="#fff" opacity="0.8"/>
                        <circle cx="362" cy="150" r="20" fill="none" stroke="#ff6b35" stroke-width="3"/>
                        
                        <text x="200" y="40" text-anchor="middle" fill="#fff" font-size="16">Peak height</text>
                        <text x="20" y="245" text-anchor="middle" fill="#fff" font-size="14">Start</text>
                        <text x="380" y="245" text-anchor="middle" fill="#fff" font-size="14">End</text>
                    </svg>
                </div>
                <div style="margin-top: 20px; font-size: 18px;">
                    <p>🎯 Real-world application: Calculating the path of a basketball shot</p>
                    <p style="margin-top: 10px; opacity: 0.8; font-size: 16px;">Just like a recipe that always works, the quadratic formula helps us find where any parabola crosses the x-axis!</p>
                </div>
            </div>
            <div class="slide-footer">
                <span class="slide-footer-text">GCSE Maths | Year 10 | Quadratic Formula</span>
                <span class="slide-number">1 / 5</span>
            </div>
        </div>
        
        <div class="slide objectives-slide">
            <div class="slide-content">
                <h2>What You'll Learn Today</h2>
                <div class="timing-info">⏱️ 50-minute lesson</div>
                <div class="objectives-container">
                    <div class="objectives-box">
                        <h3>By the end of this lesson, you will be able to:</h3>
                        <div class="objective-item">
                            <span class="objective-icon">✅</span>
                            <span>Apply the quadratic formula to solve any quadratic equation</span>
                        </div>
                        <div class="objective-item">
                            <span class="objective-icon">📊</span>
                            <span>Analyze whether equations have 0, 1, or 2 solutions</span>
                        </div>
                        <div class="objective-item">
                            <span class="objective-icon">🌍</span>
                            <span>Use the formula to solve real-world problems</span>
                        </div>
                    </div>
                    <div class="success-box">
                        <h3>Success Criteria (GCSE Grades)</h3>
                        <div class="success-item">
                            <span class="grade-badge bronze">Grade 4</span>
                            <span>Correctly substitute values into the formula</span>
                        </div>
                        <div class="success-item">
                            <span class="grade-badge silver">Grade 5-6</span>
                            <span>Solve equations and simplify answers</span>
                        </div>
                        <div class="success-item">
                            <span class="grade-badge gold">Grade 7-9</span>
                            <span>Apply to complex problems and explain the discriminant</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="slide-footer">
                <span class="slide-footer-text">GCSE Maths | Year 10 | Quadratic Formula</span>
                <span class="slide-number">2 / 5</span>
            </div>
        </div>
        
        <div class="slide starter-slide">
            <div class="slide-content">
                <h2>Quick Warm-Up Activities</h2>
                <div class="activities-grid">
                    <div class="activity-card">
                        <h3>🎯 Drag-and-Drop Matching</h3>
                        <div class="activity-time">⏱️ 3 minutes</div>
                        <p>Match equations to their a, b, c values:</p>
                        <div class="drag-drop-demo">
                            <div class="equation-box">2x² + 5x - 3 = 0</div>
                            <div class="equation-box">x² - 4x + 4 = 0</div>
                            <div class="equation-box">3x² + 7x = 0</div>
                        </div>
                    </div>
                    <div class="activity-card">
                        <h3>🤝 Think-Pair-Share</h3>
                        <div class="activity-time">⏱️ 2 minutes</div>
                        <p>Discuss with your partner:</p>
                        <div class="think-pair-share-question">
                            "How do the values of a, b, and c affect the shape and position of a parabola?"
                        </div>
                    </div>
                    <div class="activity-card">
                        <h3>✍️ Mini-Whiteboard Check</h3>
                        <div class="activity-time">⏱️ 2 minutes</div>
                        <p>Show your working for:</p>
                        <div class="whiteboard-example">
                            x² + 6x + 5 = 0<br>
                            a = ?, b = ?, c = ?
                        </div>
                    </div>
                </div>
            </div>
            <div class="slide-footer">
                <span class="slide-footer-text">GCSE Maths | Year 10 | Quadratic Formula</span>
                <span class="slide-number">3 / 5</span>
            </div>
        </div>
        
        <div class="slide activities-slide">
            <div class="slide-content">
                <h2>Guided & Independent Practice</h2>
                <div class="differentiation-banner">
                    <strong>🎯 Differentiated Support Available:</strong> Formula cards for dyslexic students | Vocabulary sheets for EAL learners | Extension tasks for advanced students
                </div>
                <div class="activities-flow">
                    <div class="activity-box">
                        <h3>📝 Worked Example Together</h3>
                        <div class="worked-example">
Solve: 2x² + 7x - 4 = 0

Step 1: Identify a = 2, b = 7, c = -4
Step 2: x = [-b ± √(b² - 4ac)] / 2a
Step 3: x = [-7 ± √(49 + 32)] / 4
Step 4: x = [-7 ± √81] / 4
Step 5: x = [-7 ± 9] / 4
Step 6: x = 0.5 or x = -4
                        </div>
                        <div class="support-note">
                            📋 Support: Step-by-step checklist available
                        </div>
                    </div>
                    <div class="activity-box">
                        <h3>👥 Scaffolded Practice</h3>
                        <div class="group-work-indicator">Pairs</div>
                        <p>Try this with your partner:</p>
                        <div class="exam-question">
                            Solve: x² - 5x + 6 = 0
                        </div>
                        <div class="differentiation-note">
                            💡 <strong>Support:</strong> Partially completed solutions available<br>
                            🚀 <strong>Challenge:</strong> Find both algebraic and graphical solutions
                        </div>
                    </div>
                    <div class="activity-box">
                        <h3>🎯 Grade 5 Exam Question</h3>
                        <div class="exam-question">
                            <strong>Context Problem:</strong><br>
                            A ball is thrown upward. Its height h (meters) after t seconds is given by:<br>
                            h = -5t² + 20t + 1.5<br>
                            When does the ball hit the ground?
                        </div>
                    </div>
                    <div class="activity-box">
                        <h3>🚀 Extension Challenge</h3>
                        <div class="exam-question" style="background: #f3e5f5; border-left-color: #7b1fa2;">
                            <strong>For High Achievers:</strong><br>
                            1. Explore when b² - 4ac < 0<br>
                            2. Derive the quadratic formula<br>
                            3. Create your own real-world problem
                        </div>
                    </div>
                </div>
            </div>
            <div class="slide-footer">
                <span class="slide-footer-text">GCSE Maths | Year 10 | Quadratic Formula</span>
                <span class="slide-number">4 / 5</span>
            </div>
        </div>
        
        <div class="slide plenary-slide">
            <div class="slide-content">
                <h2>Check Your Understanding & Next Steps</h2>
                <div class="plenary-grid">
                    <div class="plenary-section">
                        <h3>📋 Exit Ticket</h3>
                        <div class="exit-ticket">
                            <strong>Complete these 3 questions:</strong>
                            <ol>
                                <li>Solve: x² - 5x + 6 = 0</li>
                                <li>What does the ± symbol mean in the formula?</li>
                                <li>Give one real-world use of quadratics</li>
                            </ol>
                            <p style="text-align: center; margin-top: 10px;">
                                📱 Scan QR code to submit
                            </p>
                        </div>
                    </div>
                    <div class="plenary-section">
                        <h3>🚦 Self-Assessment</h3>
                        <p>How confident are you with today's learning?</p>
                        <div class="traffic-lights">
                            <div class="traffic-light red">Need<br>Help</div>
                            <div class="traffic-light amber">Getting<br>There</div>
                            <div class="traffic-light green">Confident</div>
                        </div>
                        <div class="support-note" style="margin-top: 15px;">
                            🌟 Remember: Every student can master this!
                        </div>
                    </div>
                    <div class="plenary-section">
                        <h3>📚 Homework</h3>
                        <div class="homework-box">
                            <p><strong>MathsWatch Clip 132</strong></p>
                            <p>Complete the online exercises</p>
                            <p>Due: Next lesson</p>
                        </div>
                    </div>
                    <div class="plenary-section">
                        <h3>🔮 Next Lesson Preview</h3>
                        <div class="next-lesson-preview">
                            <strong>Completing the Square</strong><br>
                            How physicists use this method to analyze projectile motion!
                        </div>
                    </div>
                </div>
                <div class="closing-message">
                    <strong>Remember:</strong> The quadratic formula is like a master key - it unlocks ANY quadratic equation! 🔑
                </div>
            </div>
            <div class="slide-footer">
                <span class="slide-footer-text">GCSE Maths | Year 10 | Quadratic Formula</span>
                <span class="slide-number">5 / 5</span>
            </div>
        </div>
    </div>
    
    <button class="download-button" onclick="downloadAsPPTX()" title="Download as PowerPoint">
        <svg class="download-icon" fill="currentColor" viewBox="0 0 20 20">
            <path d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"></path>
        </svg>
        Download PPTX
    </button>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/PptxGenJS/3.12.0/pptxgen.bundle.js"></script>
    <script>
        function downloadAsPPTX() {
            // Create a new presentation
            let pptx = new PptxGenJS();
            
            // Set presentation properties
            pptx.author = 'GCSE Maths Teacher';
            pptx.company = 'Year 10 GCSE Mathematics';
            pptx.subject = 'Quadratic Formula';
            pptx.title = 'The Quadratic Formula - Teaching Demonstration';
            
            // Define slide master with footer
            pptx.defineSlideMaster({
                title: 'MASTER_SLIDE',
                background: { color: 'FFFFFF' },
                objects: [
                    {
                        'text': {
                            text: 'GCSE Maths | Year 10 | Quadratic Formula',
                            options: { x: 0, y: '95%', w: '100%', h: 0.5, align: 'center', fontSize: 10, color: '666666' }
                        }
                    }
                ]
            });
            
            // Slide 1: Title Slide
            let slide1 = pptx.addSlide({ masterName: 'MASTER_SLIDE' });
            slide1.background = { color: '667EEA' };
            slide1.addText('The Quadratic Formula', { 
                x: 0, y: '25%', w: '100%', h: 1, 
                fontSize: 44, bold: true, color: 'FFFFFF', align: 'center' 
            });
            slide1.addText('A 5-minute teaching demonstration', { 
                x: 0, y: '40%', w: '100%', h: 0.5, 
                fontSize: 24, color: 'FFFFFF', align: 'center' 
            });
            slide1.addText('Year 10 GCSE Mathematics (Ages 14-15)', { 
                x: 0, y: '50%', w: '100%', h: 0.5, 
                fontSize: 20, color: 'FFFFFF', align: 'center' 
            });
            slide1.addText('🎯 Real-world application: Calculating the path of a basketball shot', { 
                x: 0, y: '70%', w: '100%', h: 0.5, 
                fontSize: 18, color: 'FFFFFF', align: 'center' 
            });
            slide1.addText('1 / 5', { x: '90%', y: '95%', w: '10%', h: 0.5, fontSize: 10, color: '666666' });
            
            // Slide 2: Learning Objectives
            let slide2 = pptx.addSlide({ masterName: 'MASTER_SLIDE' });
            slide2.background = { color: 'F8F9FA' };
            slide2.addText('What You\'ll Learn Today', { 
                x: 0.5, y: 0.5, w: 9, h: 1, 
                fontSize: 32, bold: true, color: '2C5AA0', align: 'center' 
            });
            
            // Learning objectives box
            slide2.addText('By the end of this lesson, you will be able to:', { 
                x: 0.5, y: 1.8, w: 4, h: 0.5, 
                fontSize: 18, bold: true, color: '2C5AA0' 
            });
            slide2.addText('✅ Apply the quadratic formula to solve any quadratic equation\n\n📊 Analyze whether equations have 0, 1, or 2 solutions\n\n🌍 Use the formula to solve real-world problems', { 
                x: 0.5, y: 2.5, w: 4, h: 3, 
                fontSize: 14, color: '333333', valign: 'top' 
            });
            
            // Success criteria box
            slide2.addText('Success Criteria (GCSE Grades)', { 
                x: 5, y: 1.8, w: 4, h: 0.5, 
                fontSize: 18, bold: true, color: '2C5AA0' 
            });
            slide2.addText('Grade 4: Correctly substitute values into the formula\n\nGrade 5-6: Solve equations and simplify answers\n\nGrade 7-9: Apply to complex problems and explain the discriminant', { 
                x: 5, y: 2.5, w: 4, h: 3, 
                fontSize: 14, color: '333333', valign: 'top' 
            });
            
            slide2.addText('⏱️ 50-minute lesson', { 
                x: 7.5, y: 0.5, w: 2, h: 0.5, 
                fontSize: 14, bold: true, color: '2C5AA0', align: 'right' 
            });
            slide2.addText('2 / 5', { x: '90%', y: '95%', w: '10%', h: 0.5, fontSize: 10, color: '666666' });
            
            // Slide 3: Starter Activities
            let slide3 = pptx.addSlide({ masterName: 'MASTER_SLIDE' });
            slide3.background = { color: 'FFF3E0' };
            slide3.addText('Quick Warm-Up Activities', { 
                x: 0.5, y: 0.5, w: 9, h: 1, 
                fontSize: 32, bold: true, color: 'F57C00', align: 'center' 
            });
            
            // Activity 1
            slide3.addText('🎯 Drag-and-Drop Matching', { 
                x: 0.5, y: 1.8, w: 3, h: 0.5, 
                fontSize: 16, bold: true, color: 'F57C00' 
            });
            slide3.addText('⏱️ 3 minutes\n\nMatch equations to their a, b, c values:\n• 2x² + 5x - 3 = 0\n• x² - 4x + 4 = 0\n• 3x² + 7x = 0', { 
                x: 0.5, y: 2.3, w: 3, h: 2.5, 
                fontSize: 12, color: '333333', valign: 'top' 
            });
            
            // Activity 2
            slide3.addText('🤝 Think-Pair-Share', { 
                x: 3.5, y: 1.8, w: 3, h: 0.5, 
                fontSize: 16, bold: true, color: 'F57C00' 
            });
            slide3.addText('⏱️ 2 minutes\n\nDiscuss with your partner:\n\n"How do the values of a, b, and c affect the shape and position of a parabola?"', { 
                x: 3.5, y: 2.3, w: 3, h: 2.5, 
                fontSize: 12, color: '333333', valign: 'top' 
            });
            
            // Activity 3
            slide3.addText('✍️ Mini-Whiteboard Check', { 
                x: 6.5, y: 1.8, w: 3, h: 0.5, 
                fontSize: 16, bold: true, color: 'F57C00' 
            });
            slide3.addText('⏱️ 2 minutes\n\nShow your working for:\n\nx² + 6x + 5 = 0\na = ?, b = ?, c = ?', { 
                x: 6.5, y: 2.3, w: 3, h: 2.5, 
                fontSize: 12, color: '333333', valign: 'top' 
            });
            slide3.addText('3 / 5', { x: '90%', y: '95%', w: '10%', h: 0.5, fontSize: 10, color: '666666' });
            
            // Slide 4: Main Activities
            let slide4 = pptx.addSlide({ masterName: 'MASTER_SLIDE' });
            slide4.background = { color: 'E8F5E9' };
            slide4.addText('Guided & Independent Practice', { 
                x: 0.5, y: 0.5, w: 9, h: 0.8, 
                fontSize: 32, bold: true, color: '388E3C', align: 'center' 
            });
            
            slide4.addText('🎯 Differentiated Support Available: Formula cards for dyslexic students | Vocabulary sheets for EAL learners | Extension tasks', { 
                x: 0.5, y: 1.3, w: 9, h: 0.5, 
                fontSize: 12, color: '0097A7', align: 'center' 
            });
            
            // Worked Example
            slide4.addText('📝 Worked Example Together', { 
                x: 0.5, y: 2, w: 4.5, h: 0.4, 
                fontSize: 16, bold: true, color: '388E3C' 
            });
            slide4.addText('Solve: 2x² + 7x - 4 = 0\n\nStep 1: a = 2, b = 7, c = -4\nStep 2: x = [-b ± √(b² - 4ac)] / 2a\nStep 3: x = [-7 ± √81] / 4\nStep 4: x = 0.5 or x = -4', { 
                x: 0.5, y: 2.4, w: 4.5, h: 2, 
                fontSize: 11, color: '333333', fontFace: 'Courier New' 
            });
            
            // Other activities
            slide4.addText('👥 Scaffolded Practice (Pairs)', { 
                x: 5, y: 2, w: 4.5, h: 0.4, 
                fontSize: 16, bold: true, color: '388E3C' 
            });
            slide4.addText('🎯 Grade 5 Exam Question', { 
                x: 0.5, y: 4.5, w: 4.5, h: 0.4, 
                fontSize: 16, bold: true, color: '388E3C' 
            });
            slide4.addText('🚀 Extension Challenge', { 
                x: 5, y: 4.5, w: 4.5, h: 0.4, 
                fontSize: 16, bold: true, color: '388E3C' 
            });
            slide4.addText('4 / 5', { x: '90%', y: '95%', w: '10%', h: 0.5, fontSize: 10, color: '666666' });
            
            // Slide 5: Plenary
            let slide5 = pptx.addSlide({ masterName: 'MASTER_SLIDE' });
            slide5.background = { color: 'F3E5F5' };
            slide5.addText('Check Your Understanding & Next Steps', { 
                x: 0.5, y: 0.5, w: 9, h: 0.8, 
                fontSize: 32, bold: true, color: '7B1FA2', align: 'center' 
            });
            
            // Exit Ticket
            slide5.addText('📋 Exit Ticket', { 
                x: 0.5, y: 1.5, w: 4.5, h: 0.4, 
                fontSize: 16, bold: true, color: '7B1FA2' 
            });
            slide5.addText('Complete these 3 questions:\n1. Solve: x² - 5x + 6 = 0\n2. What does ± mean?\n3. Give one real-world use', { 
                x: 0.5, y: 1.9, w: 4.5, h: 1.5, 
                fontSize: 12, color: '333333' 
            });
            
            // Self-Assessment
            slide5.addText('🚦 Self-Assessment', { 
                x: 5, y: 1.5, w: 4.5, h: 0.4, 
                fontSize: 16, bold: true, color: '7B1FA2' 
            });
            slide5.addText('How confident are you?\n\n🔴 Need Help\n🟡 Getting There\n🟢 Confident', { 
                x: 5, y: 1.9, w: 4.5, h: 1.5, 
                fontSize: 12, color: '333333' 
            });
            
            // Homework & Next Lesson
            slide5.addText('📚 Homework: MathsWatch Clip 132', { 
                x: 0.5, y: 3.8, w: 4.5, h: 0.4, 
                fontSize: 14, bold: true, color: '7B1FA2' 
            });
            slide5.addText('🔮 Next Lesson: Completing the Square', { 
                x: 5, y: 3.8, w: 4.5, h: 0.4, 
                fontSize: 14, bold: true, color: '7B1FA2' 
            });
            
            slide5.addText('Remember: The quadratic formula is like a master key - it unlocks ANY quadratic equation! 🔑', { 
                x: 0.5, y: 5, w: 9, h: 0.8, 
                fontSize: 16, bold: true, color: '7B1FA2', align: 'center' 
            });
            slide5.addText('5 / 5', { x: '90%', y: '95%', w: '10%', h: 0.5, fontSize: 10, color: '666666' });
            
            // Save the presentation
            pptx.writeFile({ fileName: 'Quadratic_Formula_Year10_GCSE.pptx' })
                .then(fileName => {
                    console.log(`Created file: ${fileName}`);
                });
        }
    </script>
</body>
</html>